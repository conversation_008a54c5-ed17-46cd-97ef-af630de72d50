#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试转换结果并创建示例数据
"""

import pandas as pd
from process_report_template import hex_to_rtf_to_text

def create_test_data():
    """创建包含用户示例内容的测试数据"""
    
    # 用户期望的示例内容
    wys_example = """左上颌骨前磨牙区至右侧翼腭窝、右上颌窦内见不规则浸润性生长软组织肿物影像,右侧鼻腔外侧壁、下鼻甲、上颌骨腭突及牙槽突、蝶骨翼板及翼腭窝骨质破坏。右侧眶底未受累及。双侧颈部见多枚淋巴结影像，右侧I-II区淋巴结肿大明显，右颈I区多枚直径1.0厘米球形淋巴结，II区淋巴结较大者约2.0*1.5*1.5厘米，液化不明显。"""
    
    wys_example = """双侧上颌骨、右上颌窦恶性肿物可能性较大
右颈部I-II区淋巴结转移可能性较大
建议临床进一步检查"""
    
    print("期望的WYS内容:")
    print(wys_example)
    print("\n期望的WYG内容:")
    print(wys_example)
    
    # 将中文文本转换为RTF格式的十六进制编码（模拟过程）
    def text_to_rtf_hex(text):
        """将文本转换为RTF格式的十六进制编码"""
        # 创建RTF格式
        rtf_content = r"{\rtf1\ansi\ansicpg936\deff0\deflang1033\deflangfe2052"
        rtf_content += r"{\fonttbl{\f0\fnil\fcharset134 \'cb\'ce\'cc\'e5;}}"
        rtf_content += r"\viewkind4\uc1\pard\lang2052\f0\fs24 "
        
        # 将中文字符转换为\'xx\'xx格式
        for char in text:
            if ord(char) > 127:  # 中文字符
                try:
                    gb_bytes = char.encode('gb2312')
                    if len(gb_bytes) == 2:
                        rtf_content += f"\\'{gb_bytes[0]:02x}\\'{gb_bytes[1]:02x}"
                    else:
                        rtf_content += f"\\'{gb_bytes[0]:02x}"
                except:
                    rtf_content += char
            else:
                if char == '\n':
                    rtf_content += r"\par "
                else:
                    rtf_content += char
        
        rtf_content += r"\par }"
        
        # 转换为十六进制
        hex_string = rtf_content.encode('latin-1').hex()
        return "0x" + hex_string
    
    # 测试转换
    wys_hex = text_to_rtf_hex(wys_example)
    wys_hex = text_to_rtf_hex(wys_example)
    
    print(f"\n生成的WYS十六进制长度: {len(wys_hex)}")
    print(f"生成的WYG十六进制长度: {len(wys_hex)}")
    
    # 测试反向转换
    print("\n测试反向转换:")
    converted_wys = hex_to_rtf_to_text(wys_hex)
    converted_wys = hex_to_rtf_to_text(wys_hex)
    
    print("转换后的WYS:")
    print(converted_wys)
    print("\n转换后的WYG:")
    print(converted_wys)
    
    return wys_hex, wys_hex

def test_existing_data():
    """测试现有数据的转换效果"""
    print("\n" + "="*60)
    print("测试现有数据转换效果")
    print("="*60)
    
    # 读取处理后的文件
    try:
        df = pd.read_csv("报告模板1_处理后.csv", encoding='utf-8')
        
        # 找到有实际内容的行
        for i, row in df.iterrows():
            if len(str(row['WYS'])) > 20 and 'cpg936' not in str(row['WYS']):
                print(f"\n行 {i+1} - {row['TemplateName']}:")
                print(f"WYS: {row['WYS']}")
                if len(str(row['WYG'])) > 10 and 'cpg936' not in str(row['WYG']):
                    print(f"WYG: {row['WYG']}")
                print("-" * 40)
                
                # 只显示前5个有效结果
                if i >= 5:
                    break
                    
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    print("="*60)
    print("RTF转换测试")
    print("="*60)
    
    # 创建和测试示例数据
    create_test_data()
    
    # 测试现有数据
    test_existing_data()
