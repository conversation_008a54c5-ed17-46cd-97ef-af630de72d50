#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试RTF解析过程
"""

import re

def debug_hex_to_rtf(hex_string):
    """调试十六进制到RTF的转换过程"""
    print("=" * 60)
    print("调试RTF转换过程")
    print("=" * 60)
    
    # 移除'0x'前缀
    if hex_string.startswith('0x'):
        hex_string = hex_string[2:]
    
    print(f"十六进制字符串长度: {len(hex_string)}")
    print(f"十六进制字符串前100字符: {hex_string[:100]}")
    
    # 转换为RTF内容
    rtf_content = bytes.fromhex(hex_string).decode('latin-1')
    print(f"\nRTF内容长度: {len(rtf_content)}")
    print(f"RTF内容前200字符:")
    print(rtf_content[:200])
    
    # 查找中文字符编码
    chinese_chars = re.findall(r"\\'([0-9a-fA-F]{2})", rtf_content)
    print(f"\n找到的中文字符编码数量: {len(chinese_chars)}")
    print(f"前20个编码: {chinese_chars[:20]}")
    
    # 尝试解码中文字符
    if chinese_chars:
        print("\n解码中文字符:")
        for i, hex_code in enumerate(chinese_chars[:10]):
            try:
                char = bytes.fromhex(hex_code).decode('gb2312', errors='ignore')
                print(f"  {hex_code} -> {char}")
            except:
                print(f"  {hex_code} -> 解码失败")
    
    return rtf_content

def improved_clean_rtf_text(rtf_content):
    """改进的RTF文本清理函数"""
    if not rtf_content:
        return ""
    
    print("\n" + "=" * 40)
    print("开始清理RTF文本")
    print("=" * 40)
    
    text = rtf_content
    print(f"原始RTF长度: {len(text)}")
    
    # 先处理中文字符编码（\\'xx格式）
    # 中文字符在GB2312中是双字节编码，需要成对处理
    chinese_matches = re.findall(r"\\'([0-9a-fA-F]{2})", text)
    print(f"找到中文字符编码: {len(chinese_matches)}个")

    # 将所有的\'xx编码提取出来，然后成对解码
    def decode_chinese_pairs(text):
        # 找到所有的\'xx模式
        hex_codes = re.findall(r"\\'([0-9a-fA-F]{2})", text)

        # 成对处理十六进制编码
        decoded_chars = []
        for i in range(0, len(hex_codes), 2):
            if i + 1 < len(hex_codes):
                # 两个字节组合成一个中文字符
                byte1 = hex_codes[i]
                byte2 = hex_codes[i + 1]
                try:
                    # 组合两个字节
                    combined_bytes = bytes.fromhex(byte1 + byte2)
                    # 尝试用GB2312解码
                    char = combined_bytes.decode('gb2312', errors='ignore')
                    if char and char.isprintable():
                        decoded_chars.append(char)
                    else:
                        decoded_chars.append('')
                except:
                    decoded_chars.append('')
            else:
                # 奇数个字节，单独处理
                try:
                    char = bytes.fromhex(hex_codes[i]).decode('gb2312', errors='ignore')
                    decoded_chars.append(char)
                except:
                    decoded_chars.append('')

        # 替换原文本中的编码
        result = text
        char_index = 0
        def replace_pair(match):
            nonlocal char_index
            if char_index < len(decoded_chars):
                char = decoded_chars[char_index]
                char_index += 1
                return char
            return ''

        result = re.sub(r"\\'[0-9a-fA-F]{2}", replace_pair, result)
        return result

    text = decode_chinese_pairs(text)
    print(f"解码中文字符后长度: {len(text)}")
    print(f"解码后前100字符: {text[:100]}")
    
    # 移除RTF控制字符，但保留文本内容
    # 移除字体表
    text = re.sub(r'\\fonttbl\{.*?\}', '', text, flags=re.DOTALL)
    
    # 移除RTF头部控制命令
    text = re.sub(r'\\rtf\d+', '', text)
    text = re.sub(r'\\ansi\\ansicpg\d+', '', text)
    text = re.sub(r'\\deff\d+', '', text)
    text = re.sub(r'\\deflang\d+', '', text)
    text = re.sub(r'\\deflangfe\d+', '', text)
    
    # 移除视图和格式控制
    text = re.sub(r'\\viewkind\d+', '', text)
    text = re.sub(r'\\uc\d+', '', text)
    text = re.sub(r'\\pard', '', text)
    text = re.sub(r'\\nowidctlpar', '', text)
    text = re.sub(r'\\qj', '', text)
    text = re.sub(r'\\kerning\d+', '', text)
    text = re.sub(r'\\sb\d+', '', text)
    text = re.sub(r'\\sa\d+', '', text)
    
    # 处理字体和大小
    text = re.sub(r'\\f\d+', '', text)
    text = re.sub(r'\\fs\d+', '', text)
    text = re.sub(r'\\lang\d+', '', text)
    
    # 处理段落分隔符
    text = re.sub(r'\\par\s*', '\n', text)
    
    print(f"移除控制字符后长度: {len(text)}")
    print(f"移除控制字符后前100字符: {text[:100]}")
    
    # 移除剩余的RTF控制字符
    text = re.sub(r'\\[a-zA-Z]+\d*\s*', '', text)
    text = re.sub(r'\\[^a-zA-Z\s]', '', text)
    
    # 移除大括号
    text = re.sub(r'[{}]', '', text)
    
    print(f"最终清理后长度: {len(text)}")
    print(f"最终清理后前200字符: {text[:200]}")
    
    # 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s+', '\n', text)
    text = re.sub(r'\s+\n', '\n', text)
    text = text.strip()
    
    return text

# 测试一个具体的十六进制字符串
test_hex = "0x7B5C727466315C616E73695C616E73696370673933365C64656666305C6465666C616E67313033335C6465666C616E676665323035327B5C666F6E7474626C7B5C66305C666E696C5C66707271325C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66315C666E696C5C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66325C6673776973735C66707271325C666368617273657430204D6963726F736F66742053616E732053657269663B7D7B5C66335C66726F6D616E5C66707271325C6663686172736574302054696D6573204E657720526F6D616E3B7D7D0A5C766965776B696E64345C7563315C706172645C6E6F77696463746C7061725C736231355C736131355C716A5C6C616E67323035325C66305C667332385C2764335C2764325C2762325C2765305C2766325C2761325C2763665C2763325C2763665C2764395C2762355C2762635C2762395C2764635C2764375C2764665C2764305C2764305C2763375C2766385C2762355C2762635C2762395C2764635C2763375C2762305C66315C2764365C2764305C66305C2762365C2763655C2762635C2766625C2764325C2762625C2763635C2766355C2764375C2762345C2762385C2764665C2763335C2764635C2762365C2763385C2764335C2762305C2761335C2761635C2764375C2765655C2762345C2766335C2762655C2762365C2764345C27626331352E36375C6632206D6D5C66305C2761335C2761635C2763335C2764635C2762365C2763385C2762345C2766335C2764365C2763325C2762655C2766395C2764345C2763385C2761335C2761635C2764335C2764325C2762325C2765305C2766325C2761325C2763665C2763325C2763665C2764395C2763665C2764395C2763635C2765355C2763375C2766385C2764335C2766325C2763655C2762345C2762635C2766625C2764325C2765635C2762335C2761335C2762385C2764665C2763335C2764635C2762365C2763385C2764335C2762305C2761315C2761335C66335C7061720A5C706172645C736231355C736131355C66305C7061720A7D"

if __name__ == "__main__":
    rtf_content = debug_hex_to_rtf(test_hex)
    final_text = improved_clean_rtf_text(rtf_content)
    
    print("\n" + "=" * 60)
    print("最终结果:")
    print("=" * 60)
    print(final_text)
