#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告模板处理脚本
处理CSV文件中WYS和WYG字段的十六进制编码，转换为RTF格式，然后提取纯文本内容
"""

import pandas as pd
import re
import os
import sys
import chardet


def hex_to_rtf_to_text(hex_string):
    """
    将十六进制字符串转换为RTF格式，然后提取纯文本内容
    
    Args:
        hex_string (str): 十六进制编码的字符串，以'0x'开头
        
    Returns:
        str: 提取的纯文本内容，保留换行格式
    """
    if not hex_string or hex_string == '0x' or pd.isna(hex_string):
        return ""
    
    try:
        # 移除'0x'前缀
        if hex_string.startswith('0x'):
            hex_string = hex_string[2:]
        
        # 将十六进制转换为字节，然后解码为RTF内容
        rtf_content = bytes.fromhex(hex_string).decode('latin-1')
        
        # 从RTF内容中提取纯文本
        plain_text = clean_rtf_text(rtf_content)
        
        return plain_text
        
    except Exception as e:
        print(f"处理十六进制字符串时出错: {e}")
        return ""


def clean_rtf_text(rtf_content):
    """
    从RTF内容中提取纯文本，保留换行格式

    Args:
        rtf_content (str): RTF格式的内容

    Returns:
        str: 清理后的纯文本内容
    """
    if not rtf_content:
        return ""

    text = rtf_content

    # 先处理中文字符编码（\\'xx格式）
    # 使用更精确的方法：直接替换成对的字节
    def decode_chinese_pairs(text):
        # 使用正则表达式匹配成对的\'xx\'xx模式
        def replace_chinese_pair(match):
            hex1 = match.group(1)
            hex2 = match.group(2)
            try:
                combined_bytes = bytes.fromhex(hex1 + hex2)
                char = combined_bytes.decode('gb2312', errors='ignore')
                return char if char and char.isprintable() else ''
            except:
                return ''

        # 先处理成对的中文字符编码
        result = re.sub(r"\\'([0-9a-fA-F]{2})\\'([0-9a-fA-F]{2})", replace_chinese_pair, text)

        # 处理剩余的单个字节编码
        def replace_single_byte(match):
            hex_code = match.group(1)
            try:
                char = bytes.fromhex(hex_code).decode('gb2312', errors='ignore')
                return char if char and char.isprintable() else ''
            except:
                return ''

        result = re.sub(r"\\'([0-9a-fA-F]{2})", replace_single_byte, result)
        return result

    text = decode_chinese_pairs(text)

    # 移除字体表
    text = re.sub(r'\\fonttbl\{.*?\}', '', text, flags=re.DOTALL)

    # 移除RTF头部控制命令
    text = re.sub(r'\\rtf\d+', '', text)
    text = re.sub(r'\\ansi\\ansicpg\d+', '', text)
    text = re.sub(r'\\deff\d+', '', text)
    text = re.sub(r'\\deflang\d+', '', text)
    text = re.sub(r'\\deflangfe\d+', '', text)

    # 移除视图和格式控制
    text = re.sub(r'\\viewkind\d+', '', text)
    text = re.sub(r'\\uc\d+', '', text)
    text = re.sub(r'\\pard', '', text)
    text = re.sub(r'\\nowidctlpar', '', text)
    text = re.sub(r'\\qj', '', text)
    text = re.sub(r'\\kerning\d+', '', text)
    text = re.sub(r'\\sb\d+', '', text)
    text = re.sub(r'\\sa\d+', '', text)

    # 处理字体和大小
    text = re.sub(r'\\f\d+', '', text)
    text = re.sub(r'\\fs\d+', '', text)
    text = re.sub(r'\\lang\d+', '', text)

    # 处理段落分隔符
    text = re.sub(r'\\par\s*', '\n', text)

    # 移除剩余的RTF控制字符
    text = re.sub(r'\\[a-zA-Z]+\d*\s*', '', text)
    text = re.sub(r'\\[^a-zA-Z\s]', '', text)

    # 移除大括号
    text = re.sub(r'[{}]', '', text)

    # 移除字体名称等无关内容
    text = re.sub(r'Microsoft Sans Serif', '', text)
    text = re.sub(r'Times New Roman', '', text)
    text = re.sub(r'宋体', '', text)

    # 清理多余的空白字符和分号
    text = re.sub(r';+', '', text)
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s+', '\n', text)
    text = re.sub(r'\s+\n', '\n', text)
    text = text.strip()

    return text


def detect_encoding(file_path):
    """
    检测文件编码

    Args:
        file_path (str): 文件路径

    Returns:
        str: 检测到的编码格式
    """
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']


def process_csv_file(input_file, output_file=None):
    """
    处理CSV文件，转换WYS和WYG字段中的十六进制编码

    Args:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径，如果为None则覆盖原文件
    """
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return

    try:
        # 检测文件编码
        encoding = detect_encoding(input_file)
        print(f"检测到文件编码: {encoding}")

        # 读取CSV文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_csv(input_file, encoding=encoding)
        
        # 检查是否存在WYS和WYG字段
        if 'WYS' not in df.columns or 'WYG' not in df.columns:
            print("错误：CSV文件中未找到WYS或WYG字段")
            return
        
        print(f"找到 {len(df)} 行数据")
        
        # 处理WYS字段
        print("正在处理WYS字段...")
        df['WYS'] = df['WYS'].apply(hex_to_rtf_to_text)
        
        # 处理WYG字段
        print("正在处理WYG字段...")
        df['WYG'] = df['WYG'].apply(hex_to_rtf_to_text)
        
        # 保存结果
        if output_file is None:
            output_file = input_file
        
        print(f"正在保存结果到: {output_file}")
        df.to_csv(output_file, index=False, encoding='utf-8')
        
        print("处理完成！")
        
        # 显示一些处理结果示例
        print("\n处理结果示例:")
        for i, row in df.head(3).iterrows():
            if row['WYS'].strip():
                print(f"\n行 {i+1} - WYS:")
                print(row['WYS'][:200] + "..." if len(row['WYS']) > 200 else row['WYS'])
            if row['WYG'].strip():
                print(f"\n行 {i+1} - WYG:")
                print(row['WYG'][:200] + "..." if len(row['WYG']) > 200 else row['WYG'])
        
    except Exception as e:
        print(f"处理文件时出错: {e}")


def main():
    """主函数"""
    input_file = "报告模板1.csv"
    output_file = "报告模板1_处理后.csv"
    
    print("=" * 50)
    print("报告模板处理工具")
    print("=" * 50)
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        print("请确保文件在当前目录下")
        return
    
    # 处理CSV文件
    process_csv_file(input_file, output_file)


if __name__ == "__main__":
    main()
