hex_string = "0x7B5C727466315C616E73695C616E73696370673933365C64656666305C6465666C616E67313033335C6465666C616E676665323035327B5C666F6E7474626C7B5C66305C666D6F6465726E5C66707271365C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66315C666E696C5C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66325C666E696C5C666368617273657430204D6963726F736F66742053616E732053657269663B7D7D0A5C766965776B696E64345C7563315C706172645C736231355C736131355C6C616E67323035325C66305C667332385C2763385C2761625C2762665C2764615C2764315C2763305C2762325C2764625C2762395C2763375C2762335C2763615C2762325C2762625C2763645C2761635C2762335C2763635C2762365C2763385C2763655C2766635C2763615C2764355C2761335C2761635C2766325C2761325C2762395C2763375C2762395C2763375C66315C2763365C2761345C66305C2764365C2763615C2763315C2761635C2764305C2766385C2761335C2761635C2763655C2762345C2762635C2766625C2763335C2766375C2763385C2762375C2764365C2764375C2763655C2765665C2764355C2762635C2763655C2762625C2762625C2766325C2763375C2764365C2762375C2762385C2764355C2766375C2763665C2766335C2761315C2761335C66325C667332395C7061720A5C7061720A5C7061720A5C706172645C7061720A7D"
hex_string = hex_string[2:]  # Remove '0x' prefix
rtf_content = bytes.fromhex(hex_string).decode('latin-1')
with open("output1.rtf", "w", encoding="latin-1") as f:
    f.write(rtf_content)


hex_string2 = "0x7B5C727466315C616E73695C616E73696370673933365C64656666305C6465666C616E67313033335C6465666C616E676665323035327B5C666F6E7474626C7B5C66305C666E696C5C666368617273657430204D6963726F736F66742053616E732053657269663B7D7B5C66315C666D6F6465726E5C66707271365C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7D0A5C766965776B696E64345C7563315C706172645C736231355C736131355C6C616E67323035325C66305C6673323920312E5C66315C667332385C2766325C2761325C2762395C2763375C2763655C2762345C2762635C2766625C2763335C2766375C2763385C2762375C2764365C2764375C2763655C2765665C2764355C2762635C2763655C2762625C2762625C2766325C2763375C2764365C2762375C2762385C2764355C2766375C2763665C2766335C2761335C2761635C2763375C2765625C2762645C2765315C2762615C2763665C2763315C2764395C2762345C2762325C2761335C2762625C7061720A322E5C2763385C2761625C2762665C2764615C2764315C2763305C2764365C2764635C2764315C2764375C2761315C2761335C66305C667332395C7061720A5C706172645C7061720A7D"
hex_string2 = hex_string2[2:]  # Remove '0x' prefix
rtf_content = bytes.fromhex(hex_string2).decode('latin-1')
with open("output2.rtf", "w", encoding="latin-1") as f:
    f.write(rtf_content)