#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试特定TemplateGuid的RTF解析
"""

import re

def debug_specific_hex():
    """调试特定的十六进制字符串"""
    
    # WYS字段的十六进制数据
    wys_hex = "0x7B5C727466315C616E73695C616E73696370673933365C64656666305C6465666C616E67313033335C6465666C616E676665323035327B5C666F6E7474626C7B5C66305C666E696C5C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66315C666E696C5C666368617273657430204D6963726F736F66742053616E732053657269663B7D7D0A5C766965776B696E64345C7563315C706172645C6B65726E696E67325C66305C66733234202D202D5C2762325C2762665C2763655C2762625C2764345C2765635C2764335C2762305C2763365C2761632B5C2762395C2761365C2763345C2764635C2763365C2761635C2761335C2762612D202D5C2762325C2762665C2763655C2762625C2764365C2766375C2762355C2762635C2762395C2764635C2762345C2764365C2763665C2762385C2762655C2766395C2764345C2763385C2761335C2761635C2764305C2763655C2763635C2761635C2762625C2766395C2762315C2762655C2764355C2766645C2762335C2761335C2761335C2761635C2762375C2764365C2764365C2761375C2762355C2762635C2762395C2764635C2763665C2764395C2763355C2764645C2762335C2765345C2764335C2761665C2763665C2764345C2763665C2766315C2762615C2763335C2761335C2761635C2763655C2762345C2762635C2766625C2763345C2761395C2763395C2764325C2762355C2762635C2762395C2764635C2763305C2761395C2764355C2763355C2762315C2765645C2763665C2764365C2761315C2761335C2763355C2763355C2762665C2764355C2762395C2761365C2763345C2764635C2762615C2763335C2761315C2761335C6C616E67323035325C6B65726E696E67305C66315C7061720A7D"
    
    # WYG字段的十六进制数据
    wyg_hex = "0x7B5C727466315C616E73695C616E73696370673933365C64656666305C6465666C616E67313033335C6465666C616E676665323035327B5C666F6E7474626C7B5C66305C666E696C5C6663686172736574313334205C2763625C2763655C2763635C2765353B7D7B5C66315C666E696C5C666368617273657430204D6963726F736F66742053616E732053657269663B7D7D0A5C766965776B696E64345C7563315C706172645C6B65726E696E67325C66305C667332345C2762345C2766335C2764365C2763325C2764355C2766645C2762335C2761332D202D5C2762325C2762665C2763655C2762625C2764345C2765635C2764335C2762305C2762315C2765645C2763665C2764365C2761315C2761335C6C616E67323035325C6B65726E696E67305C66315C7061720A7D"
    
    print("="*80)
    print("调试TemplateGuid: 000906d6-d5c4-429c-a02b-d27cc15ce6e9")
    print("="*80)
    
    # 处理WYS
    print("\n处理WYS字段:")
    print("-"*40)
    wys_result = process_hex_detailed(wys_hex, "WYS")
    
    print("\n处理WYG字段:")
    print("-"*40)
    wyg_result = process_hex_detailed(wyg_hex, "WYG")
    
    print("\n"+"="*80)
    print("最终结果对比:")
    print("="*80)
    print("脚本处理的WYS:")
    print(wys_result)
    print("\n您单独测试的WYS:")
    print("- -部位造影片+功能片：- -部位主导管粗细均匀，形态基本正常，分支导管腺泡充盈显像好，未见末梢导管扩张表现。排空功能好。")
    
    print("\n脚本处理的WYG:")
    print(wyg_result)
    print("\n您单独测试的WYG:")
    print("大致正常- -部位造影表现。")

def process_hex_detailed(hex_string, field_name):
    """详细处理十六进制字符串"""
    
    if hex_string.startswith('0x'):
        hex_string = hex_string[2:]
    
    print(f"{field_name}十六进制长度: {len(hex_string)}")
    
    # 转换为RTF
    rtf_content = bytes.fromhex(hex_string).decode('latin-1')
    print(f"{field_name}RTF长度: {len(rtf_content)}")
    print(f"{field_name}RTF前200字符:")
    try:
        print(repr(rtf_content[:200]))
    except:
        print("RTF内容显示出错")
    
    # 查找所有中文字符编码
    chinese_codes = re.findall(r"\\'([0-9a-fA-F]{2})", rtf_content)
    print(f"\n{field_name}中文字符编码数量: {len(chinese_codes)}")
    print(f"{field_name}中文编码: {chinese_codes}")
    
    # 逐步解码中文字符
    print(f"\n{field_name}逐步解码中文字符:")
    decoded_chars = []
    for i in range(0, len(chinese_codes), 2):
        if i + 1 < len(chinese_codes):
            byte1 = chinese_codes[i]
            byte2 = chinese_codes[i + 1]
            try:
                combined_bytes = bytes.fromhex(byte1 + byte2)
                char = combined_bytes.decode('gb2312', errors='ignore')
                decoded_chars.append(char)
                print(f"  {byte1}{byte2} -> {char}")
            except:
                decoded_chars.append('')
                print(f"  {byte1}{byte2} -> 解码失败")
        else:
            try:
                char = bytes.fromhex(chinese_codes[i]).decode('gb2312', errors='ignore')
                decoded_chars.append(char)
                print(f"  {chinese_codes[i]} -> {char}")
            except:
                decoded_chars.append('')
                print(f"  {chinese_codes[i]} -> 解码失败")
    
    print(f"\n{field_name}解码出的中文字符: {''.join(decoded_chars)}")
    
    # 应用清理逻辑
    text = rtf_content
    
    # 替换中文字符编码
    char_index = 0
    def replace_chinese(match):
        nonlocal char_index
        if char_index < len(decoded_chars):
            char = decoded_chars[char_index]
            char_index += 1
            return char
        return ''
    
    text = re.sub(r"\\'[0-9a-fA-F]{2}", replace_chinese, text)
    print(f"\n{field_name}替换中文字符后:")
    print(text[:200])
    
    # 清理RTF控制字符
    text = re.sub(r'\\fonttbl\{.*?\}', '', text, flags=re.DOTALL)
    text = re.sub(r'\\rtf\d+', '', text)
    text = re.sub(r'\\ansi\\ansicpg\d+', '', text)
    text = re.sub(r'\\deff\d+', '', text)
    text = re.sub(r'\\deflang\d+', '', text)
    text = re.sub(r'\\deflangfe\d+', '', text)
    text = re.sub(r'\\viewkind\d+', '', text)
    text = re.sub(r'\\uc\d+', '', text)
    text = re.sub(r'\\pard', '', text)
    text = re.sub(r'\\kerning\d+', '', text)
    text = re.sub(r'\\f\d+', '', text)
    text = re.sub(r'\\fs\d+', '', text)
    text = re.sub(r'\\lang\d+', '', text)
    text = re.sub(r'\\par\s*', '\n', text)
    text = re.sub(r'\\[a-zA-Z]+\d*\s*', '', text)
    text = re.sub(r'\\[^a-zA-Z\s]', '', text)
    text = re.sub(r'[{}]', '', text)
    text = re.sub(r'Microsoft Sans Serif', '', text)
    text = re.sub(r';+', '', text)
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    print(f"\n{field_name}最终清理结果:")
    print(text)
    
    return text

if __name__ == "__main__":
    debug_specific_hex()
